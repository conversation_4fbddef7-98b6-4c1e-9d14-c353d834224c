# Nostr MCP Server

This MCP server contains tools for an LLM to interact with [Nostr](https://nostr.com) protocol relays.

Current available tools:

* [X] `publish_anon_nostr_event`: Publishes an event to relays with a random pubkey.
* [X] `publish_nostr_event`: Publishes an event to relays.
* [X] `get_nip`: Returns a NIPs markdown text by its string ID like `01` or `CC`.
* [X] `relay_info`: Returns NIP-11 information document of a relay.
* [ ] `fetch_nostr_events`: Queries multiple filters to relays and returns the matching events.
* [ ] `get_nostr_pubkey`: Returns the corresponding pubkey of private key in [config](./config.toml).
* [ ] `list_nostr_relays`: Returns the relays in the [config](./config.toml).
* [ ] `connect_relay`: Connects to a relay at runtime.
* [ ] `disconnect_relay`: Disconnects from a relay at runtime.
* [ ] `nip19_decode`: Decodes a NIP-19 entity.
* [ ] `nip19_encode`: Encodes a NIP-19 entity.
