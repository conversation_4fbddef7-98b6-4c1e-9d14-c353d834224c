package main

import (
	"log"
	"os"

	"github.com/BurntSushi/toml"
)

type Config struct {
	Nostr struct {
		PrivateKey string   `toml:"private_key"`
		Relays     []string `toml:"relays"`
	} `toml:"nostr"`
}

func LoadConfig() {
	configFile, err := os.ReadFile("config.toml")
	if err != nil {
		log.Fatal(err)
	}

	_, err = toml.Decode(string(configFile), &config)
	if err != nil {
		log.Fatal(err)
	}
}
