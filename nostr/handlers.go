package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/nbd-wtf/go-nostr"
	"github.com/nbd-wtf/go-nostr/nip19"
)

func publishAnonEventHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	content, err := request.RequireString("content")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	kind, err := request.RequireInt("kind")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	rawTags, ok := request.GetArguments()["tags"]
	if !ok {
		rawTags = new(any)
	}

	tags, ok := rawTags.(nostr.Tags)
	if !ok {
		tags = make(nostr.Tags, 0)
	}

	event := nostr.Event{
		Kind:      kind,
		Content:   content,
		Tags:      tags,
		CreatedAt: nostr.Timestamp(time.Now().Unix()),
	}

	sk := nostr.GeneratePrivateKey()

	if err := event.Sign(sk); err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	verbose := false
	if v, ok := request.GetArguments()["verbose"]; ok {
		if b, ok := v.(bool); ok {
			verbose = b
		}
	}

	success := 0
	var relayReports []string

	for _, r := range relays {
		relayURL := r.URL

		if !r.IsConnected() {
			if err := r.Connect(context.Background()); err != nil {
				if verbose {
					relayReports = append(relayReports, fmt.Sprintf("❌ %s: Connection failed - %s", relayURL, err.Error()))
				}
				continue
			}
		}

		if err := r.Publish(context.Background(), event); err != nil {
			if verbose {
				relayReports = append(relayReports, fmt.Sprintf("❌ %s: Publish failed - %s", relayURL, err.Error()))
			}
			continue
		}

		success++
		if verbose {
			relayReports = append(relayReports, fmt.Sprintf("✅ %s: Published successfully", relayURL))
		}
	}

	result := fmt.Sprintf("Event ID: %s\n\nRelay Report (%d/%d successful):\n", event.ID, success, len(relays))

	if verbose {
		for _, report := range relayReports {
			result += report + "\n"
		}
		return mcp.NewToolResultText(result), nil
	}

	return mcp.NewToolResultText(result), nil
}

func publishEventHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	content, err := request.RequireString("content")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	kind, err := request.RequireInt("kind")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	rawTags, ok := request.GetArguments()["tags"]
	if !ok {
		rawTags = new(any)
	}

	tags, ok := rawTags.(nostr.Tags)
	if !ok {
		tags = make(nostr.Tags, 0)
	}

	event := nostr.Event{
		Kind:      kind,
		Content:   content,
		Tags:      tags,
		CreatedAt: nostr.Timestamp(time.Now().Unix()),
	}

	if err := event.Sign(config.Nostr.PrivateKey); err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	verbose := false
	if v, ok := request.GetArguments()["verbose"]; ok {
		if b, ok := v.(bool); ok {
			verbose = b
		}
	}

	success := 0
	var relayReports []string

	for _, r := range relays {
		relayURL := r.URL

		if !r.IsConnected() {
			if err := r.Connect(context.Background()); err != nil {
				if verbose {
					relayReports = append(relayReports, fmt.Sprintf("❌ %s: Connection failed - %s", relayURL, err.Error()))
				}
				continue
			}
		}

		if err := r.Publish(context.Background(), event); err != nil {
			if verbose {
				relayReports = append(relayReports, fmt.Sprintf("❌ %s: Publish failed - %s", relayURL, err.Error()))
			}
			continue
		}

		success++
		if verbose {
			relayReports = append(relayReports, fmt.Sprintf("✅ %s: Published successfully", relayURL))
		}
	}

	result := fmt.Sprintf("Event ID: %s\n\nRelay Report (%d/%d successful):\n", event.ID, success, len(relays))
	if verbose {
		for _, report := range relayReports {
			result += report + "\n"
		}
		return mcp.NewToolResultText(result), nil
	}

	return mcp.NewToolResultText(result), nil
}

func getNIPHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	NIPNumber, err := request.RequireString("nip_number")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	url := fmt.Sprintf("https://raw.githubusercontent.com/nostr-protocol/nips/master/%s.md", NIPNumber)
	resp, err := http.Get(url)
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	bodyString := string(body)
	bodyString = strings.ReplaceAll(bodyString, "\n", "\n\n")

	return mcp.NewToolResultText(bodyString), nil
}

func relayInfoHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	relayURL, err := request.RequireString("relay_url")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	httpURL := strings.Replace(strings.Replace(relayURL, "ws://", "http://", 1), "wss://", "https://", 1)

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, httpURL, nil)
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	req.Header.Set("Accept", "application/nostr+json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return mcp.NewToolResultError(fmt.Sprintf("HTTP error: %d %s", resp.StatusCode, resp.Status)), nil
	}

	var info any
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&info); err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	pretty, err := json.MarshalIndent(info, "", "    ")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}

	return mcp.NewToolResultText(string(pretty)), nil
}

func fetchNostrEventsHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	args := request.GetArguments()

	filter := nostr.Filter{}
	if v, ok := args["ids"].([]interface{}); ok {
		ids := make([]string, 0, len(v))
		for _, x := range v {
			if s, ok := x.(string); ok {
				ids = append(ids, s)
			}
		}
		filter.IDs = ids
	}
	if v, ok := args["authors"].([]interface{}); ok {
		authors := make([]string, 0, len(v))
		for _, x := range v {
			if s, ok := x.(string); ok {
				authors = append(authors, s)
			}
		}
		filter.Authors = authors
	}
	if v, ok := args["kinds"].([]interface{}); ok {
		kinds := make([]int, 0, len(v))
		for _, x := range v {
			switch t := x.(type) {
			case float64:
				kinds = append(kinds, int(t))
			case int:
				kinds = append(kinds, t)
			}
		}
		filter.Kinds = kinds
	}
	if v, ok := args["since"].(float64); ok {
		ts := nostr.Timestamp(int64(v))
		filter.Since = &ts
	}
	if v, ok := args["until"].(float64); ok {
		ts := nostr.Timestamp(int64(v))
		filter.Until = &ts
	}
	if v, ok := args["limit"].(float64); ok {
		filter.Limit = int(v)
	}
	// tags can be provided as JSON string or object map[string][]string
	if s, ok := args["tags"].(string); ok && s != "" {
		m := map[string][]string{}
		if err := json.Unmarshal([]byte(s), &m); err == nil {
			filter.Tags = m
		}
	} else if rawTags, ok := args["tags"].(map[string]interface{}); ok {
		m := make(map[string][]string)
		for k, vv := range rawTags {
			arr := []string{}
			if vv2, ok := vv.([]interface{}); ok {
				for _, x := range vv2 {
					if s, ok := x.(string); ok {
						arr = append(arr, s)
					}
				}
			}
			m[k] = arr
		}
		filter.Tags = m
	}

	filters := nostr.Filters{filter}
	relayURLs := make([]string, 0, len(relays))
	for _, r := range relays {
		if r != nil {
			relayURLs = append(relayURLs, r.URL)
		}
	}
	if len(relayURLs) == 0 {
		return mcp.NewToolResultError("no connected relays"), nil
	}

	ctxQ, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	pool := nostr.NewSimplePool(ctxQ)
	defer pool.Close("done")
	ch := pool.SubManyEose(ctxQ, relayURLs, filters)
	events := make([]nostr.Event, 0)
	for re := range ch {
		if re.Event == nil {
			continue
		}
		valid, _ := re.Event.CheckSignature()
		if !valid {
			continue
		}
		events = append(events, *re.Event)
	}
	buf, err := json.MarshalIndent(events, "", "    ")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	return mcp.NewToolResultText(string(buf)), nil
}

func getNostrPubkeyHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	pub, err := nostr.GetPublicKey(config.Nostr.PrivateKey)
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	return mcp.NewToolResultText(pub), nil
}

func listNostrRelaysHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	urls := make([]string, 0, len(relays))
	for _, r := range relays {
		if r != nil {
			urls = append(urls, r.URL)
		}
	}
	out, _ := json.MarshalIndent(urls, "", "    ")
	return mcp.NewToolResultText(string(out)), nil
}

func connectRelayHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	relayURL, err := request.RequireString("relay_url")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	for _, r := range relays {
		if r != nil && r.URL == relayURL {
			return mcp.NewToolResultText("true"), nil
		}
	}
	relay, err := nostr.RelayConnect(ctx, relayURL)
	if err != nil {
		return mcp.NewToolResultText("false"), nil
	}
	relays = append(relays, relay)
	return mcp.NewToolResultText("true"), nil
}

func disconnectRelayHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	relayURL, err := request.RequireString("relay_url")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	removed := false
	newRelays := make([]*nostr.Relay, 0, len(relays))
	for _, r := range relays {
		if r == nil {
			continue
		}
		if r.URL == relayURL {
			_ = r.Close()
			removed = true
			continue
		}
		newRelays = append(newRelays, r)
	}
	relays = newRelays
	if removed {
		return mcp.NewToolResultText("true"), nil
	}
	return mcp.NewToolResultText("false"), nil
}

func nip19DecodeHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	code, err := request.RequireString("code")
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	kind, data, err := nip19.Decode(code)
	if err != nil {
		return mcp.NewToolResultError(err.Error()), nil
	}
	resp := map[string]interface{}{"type": kind, "data": data}
	buf, _ := json.MarshalIndent(resp, "", "    ")
	return mcp.NewToolResultText(string(buf)), nil
}

func nip19EncodeHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	args := request.GetArguments()
	kind, _ := args["type"].(string)
	switch kind {
	case "npub":
		pub, _ := args["pubkey"].(string)
		res, err := nip19.EncodePublicKey(pub)
		if err != nil {
			return mcp.NewToolResultError(err.Error()), nil
		}
		return mcp.NewToolResultText(res), nil
	case "nsec":
		sec, _ := args["privkey"].(string)
		res, err := nip19.EncodePrivateKey(sec)
		if err != nil {
			return mcp.NewToolResultError(err.Error()), nil
		}
		return mcp.NewToolResultText(res), nil
	case "note":
		id, _ := args["id"].(string)
		res, err := nip19.EncodeNote(id)
		if err != nil {
			return mcp.NewToolResultError(err.Error()), nil
		}
		return mcp.NewToolResultText(res), nil
	case "nevent":
		id, _ := args["id"].(string)
		relaysArg, _ := args["relays"].([]interface{})
		relays := make([]string, 0, len(relaysArg))
		for _, x := range relaysArg {
			if s, ok := x.(string); ok {
				relays = append(relays, s)
			}
		}
		res, err := nip19.EncodeEvent(id, relays, "")
		if err != nil {
			return mcp.NewToolResultError(err.Error()), nil
		}
		return mcp.NewToolResultText(res), nil
	case "nprofile":
		pub, _ := args["pubkey"].(string)
		relaysArg, _ := args["relays"].([]interface{})
		relays := make([]string, 0, len(relaysArg))
		for _, x := range relaysArg {
			if s, ok := x.(string); ok {
				relays = append(relays, s)
			}
		}
		res, err := nip19.EncodeProfile(pub, relays)
		if err != nil {
			return mcp.NewToolResultError(err.Error()), nil
		}
		return mcp.NewToolResultText(res), nil
	default:
		return mcp.NewToolResultError("unsupported type for encoding"), nil
	}
}
