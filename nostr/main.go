package main

import (
	"context"
	"log"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/nbd-wtf/go-nostr"
)

type ToolAndHandler struct {
	Tool    mcp.Tool
	Handler server.ToolHandlerFunc
}

var (
	config *Config
	relays = []*nostr.Relay{}
)

var toolHandlers = []ToolAndHandler{
	{
		Tool: mcp.NewTool("publish_anon_nostr_event",
			mcp.WithDescription(`
			Publish an anonymous (with a random public key) Nostr event (per NIP‑01) to configured relays.

			Nostr ("Notes and Other Stuff Transmitted by Relays") is a simple,
			censorship‑resistant protocol where each client holds an EC key pair
			and signs JSON events (per NIP‑01) before sending them to relays.

			A Nostr event contains:
			- "id": sha256 of serialized [0, pubkey, created_at, kind, tags, content]
			- "pubkey": hex‑encoded secp256-k1 public key of the author
			- "created_at": UNIX timestamp in seconds
			- "kind": integer indicating event type between 0 and 65535 (e.g., 1 = short text note)
			- "tags": list of tag arrays (e.g., [["e", "<event‑id>"], ["p", "<pubkey>"]])
			- "content": arbitrary string
			- "sig": Schnorr signature of the event hash

			This function:
			1. Constructs an event with "content", "kind", and optional "tags"
			2. Uses the server's configured private key to compute "id" and "sig"
			3. Publishes the signed event to all configured relays
			`),
			mcp.WithString("content",
				mcp.Description("the text or JSON content of the event"),
			),
			mcp.WithNumber("kind", mcp.Description("Nostr event kind (defaults to 1 = short text note)")),
			mcp.WithArray("tags",
				mcp.Description(`list of Nostr tags (arrays of strings),
					e.g. "[["e", "<event‑id>"], ["p", "<pubkey>"]]"`),
				mcp.Items(map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
				})),
			mcp.WithBoolean("verbose",
				mcp.Description("if true, provides a detailed report of relay publishing results and failure reasons"))),
		Handler: publishAnonEventHandler,
	},
	{
		Tool: mcp.NewTool("publish_nostr_event",
			mcp.WithDescription(`
			Publish a Nostr event (per NIP‑01) to configured relays and public key.

			Nostr ("Notes and Other Stuff Transmitted by Relays") is a simple,
			censorship‑resistant protocol where each client holds an EC key pair
			and signs JSON events (per NIP‑01) before sending them to relays.

			A Nostr event contains:
			- "id": sha256 of serialized [0, pubkey, created_at, kind, tags, content]
			- "pubkey": hex‑encoded secp256-k1 public key of the author
			- "created_at": UNIX timestamp in seconds
			- "kind": integer indicating event type between 0 and 65535 (e.g., 1 = short text note)
			- "tags": list of tag arrays (e.g., [["e", "<event‑id>"], ["p", "<pubkey>"]])
			- "content": arbitrary string
			- "sig": Schnorr signature of the event hash

			This function:
			1. Constructs an event with "content", "kind", and optional "tags"
			2. Uses the server's configured private key to compute "id" and "sig"
			3. Publishes the signed event to all configured relays
			`),
			mcp.WithString("content",
				mcp.Description("the text or JSON content of the event"),
			),
			mcp.WithNumber("kind", mcp.Description("Nostr event kind (defaults to 1 = short text note)")),
			mcp.WithArray("tags",
				mcp.Description(`list of Nostr tags (arrays of strings),
					e.g. "[["e", "<event‑id>"], ["p", "<pubkey>"]]"`),
				mcp.Items(map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
				})),
			mcp.WithBoolean("verbose",
				mcp.Description("if true, provides a detailed report of relay publishing results and failure reasons"))),
		Handler: publishEventHandler,
	},
	{
		Tool: mcp.NewTool("get_nip",
			mcp.WithDescription(`
			Fetches the content of a specified Nostr Implementation Possibility (NIP), returning its full markdown text.

			NIP IDs follow the pattern:
				- Numeric NIPs: e.g., "01", "07" — always zero-padded to two digits.
				- Alphanumeric NIPs (Hexadecimal): e.g., "CC", "7D".
			`),
			mcp.WithString("nip_number",
				mcp.Required(),
				mcp.Description(`The NIP identifier (zero-padded number or uppercase alphanumeric),
                      				e.g. "01", "07", "7D", "CC".`),
			)),
		Handler: getNIPHandler,
	},
	{
		Tool: mcp.NewTool("relay_info",
			mcp.WithDescription(`Retrieves metadata from a Nostr relay using NIP-11 (Relay Information Document).
`),
			mcp.WithString("relay_url",
				mcp.Required(),
				mcp.Description(`The WebSocket URL of the relay (e.g., "wss://relay.example.com")`),
			)),
		Handler: relayInfoHandler,
		},
		{
			Tool: mcp.NewTool("fetch_nostr_events",
				mcp.WithDescription(`Subscribe to configured relays with standard Nostr filters (NIP-01) and return matching events.`),
				mcp.WithArray("ids", mcp.Description("list of event IDs"), mcp.Items(map[string]interface{}{"type": "string"})),
				mcp.WithArray("authors", mcp.Description("list of author pubkeys"), mcp.Items(map[string]interface{}{"type": "string"})),
				mcp.WithArray("kinds", mcp.Description("list of kinds"), mcp.Items(map[string]interface{}{"type": "number"})),
				mcp.WithNumber("since", mcp.Description("earliest timestamp (inclusive)")),
				mcp.WithNumber("until", mcp.Description("latest timestamp (inclusive)")),
				mcp.WithNumber("limit", mcp.Description("maximum number of events")),
				mcp.WithString("search", mcp.Description("full-text search query if supported by relays")),
				mcp.WithString("tags", mcp.Description("JSON object of tag filters, e.g. {\"e\":[\"id\"], \"p\":[\"pubkey\"]}")),
			),
			Handler: fetchNostrEventsHandler,
		},
		{
			Tool: mcp.NewTool("get_nostr_pubkey",
				mcp.WithDescription(`Return the hex-encoded public key corresponding to the server's private key.`),
			),
			Handler: getNostrPubkeyHandler,
		},
		{
			Tool: mcp.NewTool("list_nostr_relays",
				mcp.WithDescription(`Return the list of relay URLs that the server is currently connected to.`),
			),
			Handler: listNostrRelaysHandler,
		},
		{
			Tool: mcp.NewTool("connect_relay",
				mcp.WithDescription(`Dynamically connects the running server to an additional Nostr relay.`),
				mcp.WithString("relay_url", mcp.Required(), mcp.Description(`WebSocket URL of the relay (e.g., "wss://relay.example.com")`)),
			),
			Handler: connectRelayHandler,
		},
		{
			Tool: mcp.NewTool("disconnect_relay",
				mcp.WithDescription(`Disconnects from a specified Nostr relay at runtime.`),
				mcp.WithString("relay_url", mcp.Required(), mcp.Description(`WebSocket URL of the relay (e.g., "wss://relay.example.com")`)),
			),
			Handler: disconnectRelayHandler,
		},
		{
			Tool: mcp.NewTool("nip19_decode",
				mcp.WithDescription(`Decode a NIP-19 entity into its type and data.`),
				mcp.WithString("code", mcp.Required(), mcp.Description("The NIP-19 code to decode (e.g., npub..., nsec..., note..., nevent..., nprofile...)")),
			),
			Handler: nip19DecodeHandler,
		},
		{
			Tool: mcp.NewTool("nip19_encode",
				mcp.WithDescription(`Encode a NIP-19 entity from provided fields.`),
				mcp.WithString("type", mcp.Required(), mcp.Description("Type to encode: npub | nsec | note | nevent | nprofile")),
				mcp.WithString("pubkey", mcp.Description("Hex pubkey (for npub/nprofile)")),
				mcp.WithString("privkey", mcp.Description("Hex privkey (for nsec)")),
				mcp.WithString("id", mcp.Description("Event ID (for note/nevent)")),
				mcp.WithArray("relays", mcp.Description("Relay URLs (for nevent/nprofile)"), mcp.Items(map[string]interface{}{"type": "string"})),
			),
			Handler: nip19EncodeHandler,
		},
	},
}

func main() {
	s := server.NewMCPServer(
		"Nostr",
		StringVersion(),
		server.WithToolCapabilities(false),
	)

	LoadConfig()

	for _, r := range config.Nostr.Relays {
		relay, err := nostr.RelayConnect(context.Background(), r)
		if err != nil {
			log.Printf("Error connecting to relay %s: %s", r, err)
		}

		relays = append(relays, relay)
	}

	for _, th := range toolHandlers {
		s.AddTool(th.Tool, th.Handler)
	}

	// TODO: Select between STDIO and SSE from config.
	if err := server.ServeStdio(s); err != nil {
		log.Fatal("Server error:", err)
	}
}
